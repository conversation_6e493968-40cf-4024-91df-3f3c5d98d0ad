generator client {
  provider        = "prisma-client-js"
  binaryTargets   = ["native", "rhel-openssl-1.0.x", "rhel-openssl-3.0.x"]
  previewFeatures = ["relationJoins"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  createdAt                DateTime                 @default(now()) @map("created_at")
  updatedAt                DateTime                 @default(now()) @updatedAt @map("updated_at")
  id                       String                   @id @default(uuid())
  name                     String
  email                    String                   @unique
  password                 String
  socketId                 String?                  @map("socket_id")
  companyId                String                   @map("company_id")
  isActive                 Boolean                  @default(true) @map("is_active")
  isAgent                  Boolean                  @default(true) @map("is_agent")
  conversationTicketsOwned ConversationTicket[]     @relation("TicketOwner")
  conversationTicketsAgent ConversationTicket[]     @relation("TicketAgent")
  canViewAllConversations  Boolean                  @default(true) @map("can_view_all_conversations")
  customer                 Customer[]
  promptThreads            PromptThread[]
  smsCampaign              SmsCampaign[]
  sockets                  Socket[]
  company                  Company                  @relation(fields: [companyId], references: [id])
  whatsappCampaigns        WhatsappCampaign[]
  emailCampaigns           EmailCampaign[]
  updatePasswordToken      String?                  @map("update_password_token")
  userConversationSectors  UserConversationSector[]
  roleId                   String?                  @map("role_id")
  role                     Role?                    @relation(fields: [roleId], references: [id])
  messages                 Message[]
  lastAnnouncementViewedAt DateTime?                @map("lastAnnouncementViewedAt")
  webPushSubscriptions     WebPushSubscription[]
  apiRequestsLogs          ApiRequestsLogs[]

  @@map("users")
}

enum GupshupAccountStatus {
  ACCOUNT_VIOLATION
  ACCOUNT_DISABLE
  ACCOUNT_VERIFIED
  ACCOUNT_RESTRICTED
}

model Company {
  createdAt                             DateTime                 @default(now()) @map("created_at")
  updatedAt                             DateTime                 @default(now()) @updatedAt @map("updated_at")
  id                                    String                   @id @default(uuid())
  name                                  String
  phoneNumber                           String?
  phoneNumberId                         String                   @unique @map("phone_number_id")
  whatsappBusinessId                    String?                  @map("whatsapp_business_id")
  whatsappAccessToken                   String?                  @map("whatsapp_access_token")
  firstContactMessage                   String?                  @map("first_contact_message")
  isAutomaticSortingActive              Boolean                  @default(false) @map("is_automatic_sorting_active")
  gupshupAppName                        String?                  @unique @map("gupshup_app_name")
  dailyMessageLimitOnWhatsapp           Int                      @default(1000) @map("daily_message_limit_on_whatsapp")
  monthlyMessageLimitOnWhatsapp         Int                      @default(30000) @map("monthly_message_limit_on_whatsapp")
  dailyEmailLimit                       Int                      @default(0) @map("daily_email_limit")
  monthlyEmailLimit                     Int                      @default(0) @map("monthly_email_limit")
  packageMessageLimitOnWhatsapp         Int                      @default(1000) @map("package_message_limit_on_whatsapp")
  websiteUrl                            String?                  @map("website_url")
  gupshupAppId                          String?                  @unique @map("gupshup_app_id")
  whatsappBusinessAccountId             String?                  @map("whatsapp_business_account_id")
  whatsappPhoneNumberId                 String?                  @map("whatsapp_phone_number_id")
  whatsappAppId                         String?                  @map("whatsapp_app_id")
  vtexAccountName                       String?                  @map("vtex_account_name")
  vtexAppKey                            String?                  @map("vtex_app_key")
  vtexAppToken                          String?                  @map("vtex_app_token")
  vtexStoreDomain                       String?                  @map("vtex_store_domain")
  isShopifyActive                       Boolean                  @default(false) @map("is_shopify_active")
  isVtexActive                          Boolean                  @default(false) @map("is_vtex_active")
  shopifyShopName                       String?                  @map("shopify_shop_name")
  shopifySession                        Json?                    @map("shopify_session")
  costPerMessage                        Int                      @default(50) @map("cost_per_message")
  costPer1kEmail                        Int                      @default(680) @map("cost_per_1k_email")
  shopifyAdminAccessToken               String?                  @map("shopify_admin_access_token")
  shopifyApiKey                         String?                  @map("shopify_api_key")
  shopifyApiSecretKey                   String?                  @map("shopify_api_secret_key")
  isUnboxActive                         Boolean                  @default(false) @map("is_unbox_active")
  unboxMetabaseId                       String?                  @map("unbox_metabase_id")
  productDescription                    String?                  @map("product_description")
  valueProposition                      String?                  @map("value_proposition")
  afterHoursMessage                     String?                  @map("after_hours_message")
  isActive                              Boolean                  @default(true) @map("is_active")
  monthlyMessageLimitOnSms              Int                      @default(0) @map("monthly_message_limit_on_sms")
  isVisualECommerceActive               Boolean                  @default(false) @map("is_visual_ecommerce_active")
  visualECommerceApiKey                 String?                  @map("visual_ecommerce_api_key")
  visualECommerceStoreDomain            String?                  @map("visual_ecommerce_store_domain")
  isLojaIntegradaActive                 Boolean                  @default(false) @map("is_loja_integrada_active")
  lojaIntegradaApiKey                   String?                  @map("loja_integrada_api_key")
  initialContactFlowId                  String?                  @map("initial_contact_flow_id")
  blingClientId                         String?                  @map("bling_client_id")
  blingClientSecret                     String?                  @map("bling_client_secret")
  blingRefreshToken                     String?                  @map("bling_refresh_token")
  blingAccessToken                      String?                  @map("bling_access_token")
  yampiUserToken                        String?                  @map("yampi_user_token")
  yampiUserSecretKey                    String?                  @map("yampi_user_secret_key")
  yampiAlias                            String?                  @map("yampi_alias")
  isYampiActive                         Boolean                  @default(false) @map("is_yampi_active")
  isBlingActive                         Boolean                  @default(false) @map("is_bling_active")
  allowedStoreNames                     String[]                 @default([]) @map("allowed_store_names")
  allowedSalesChannels                  String[]                 @default([]) @map("allowed_sales_channels")
  businessHours                         Json?                    @map("business_hours")
  calculateTicketMetricsByBusinessHours Boolean?                 @default(false) @map("calculate_ticket_metrics_by_business_hours")
  businessSector                        BusinessSector?          @map("business_sector")
  cnpj                                  String?                  @map("cnpj")
  razaoSocial                           String?                  @map("razao_social")
  shouldIncludeAttendantNameInMessages  Boolean                  @default(false) @map("should_include_attendant_name_in_messages")
  magazordApiDomain                     String?                  @map("magazord_api_domain")
  magazordClientToken                   String?                  @map("magazord_client_token")
  magazordClientPassword                String?                  @map("magazord_client_password")
  isMagazordActive                      Boolean                  @default(false) @map("is_magazord_active")
  wooCommerceClientId                   String?                  @map("woocommerce_client_id")
  wooCommerceClientSecret               String?                  @map("woocommerce_client_secret")
  wooCommerceStoreDomain                String?                  @map("woocommerce_store_domain")
  isWooCommerceActive                   Boolean                  @default(false) @map("is_woocommerce_active")
  magentoUser                           String?                  @map("magento_user")
  magentoApiKey                         String?                  @map("magento_api_key")
  magentoApiUrl                         String?                  @map("magento_api_url")
  magentoVersion                        String?                  @map("magento_version")
  isMagentoActive                       Boolean                  @default(false) @map("is_magento_active")
  ingresseApiKey                        String?                  @map("ingresse_api_key")
  isIngresseActive                      Boolean                  @default(false) @map("is_ingresse_active")
  omnyApiKey                            String?                  @map("omny_api_key")
  isOmnyActive                          Boolean                  @default(false) @map("is_omny_active")
  trayApiDomain                         String?                  @map("tray_api_domain")
  trayAuthCode                          String?                  @map("tray_auth_code")
  trayRefreshToken                      String?                  @map("tray_refresh_token")
  trayApiToken                          String?                  @map("tray_api_token")
  trayApiTokenExpireDate                DateTime?                @map("tray_api_token_expire_date")
  trayRefreshTokenExpireDate            DateTime?                @map("tray_refresh_token_expire_date")
  isTrayActive                          Boolean                  @default(false) @map("is_tray_active")
  omieAppKey                            String?                  @map("omie_app_key")
  omieAppSecret                         String?                  @map("omie_app_secret")
  isOmieActive                          Boolean                  @default(false) @map("is_omie_active")
  cartPandaAccessToken                  String?                  @map("cart_panda_access_token")
  cartPandaShopSlug                     String?                  @map("cart_panda_shop_slug")
  isCartPandaActive                     Boolean                  @default(false) @map("is_cart_panda_active")
  isRandomizerActive                    Boolean                  @default(false) @map("is_randomizer_active")
  linxCommerceAccessToken               String?                  @map("linx_commerce_access_token")
  linxCommerceAllowedWebsiteNames       String[]                 @default([]) @map("linx_commerce_allowed_website_names")
  isLinxCommerceActive                  Boolean                  @default(false) @map("is_linx_commerce_active")
  nuvemShopAccessToken                  String?                  @map("nuvem_shop_access_token")
  nuvemShopUserId                       String?                  @map("nuvem_shop_user_id")
  nuvemShopApiUrl                       String?                  @map("nuvem_shop_api_url")
  isNuvemShopOrderActive                Boolean                  @default(false) @map("is_nuvem_shop_order_active")
  isNuvemShopActive                     Boolean                  @default(false) @map("is_nuvem_shop_active")
  shoppubAccessToken                    String?                  @map("shoppub_access_token")
  shoppubStoreDomain                    String?                  @map("shoppub_store_domain")
  isShoppubActive                       Boolean                  @default(false) @map("is_shoppub_active")
  isTinyActive                          Boolean                  @default(false) @map("is_tiny_active")
  tinyApiToken                          String?                  @map("tiny_api_token")
  disallowedStoreNames                  String[]                 @default([]) @map("disallowed_store_names")
  disallowedSalesChannels               String[]                 @default([]) @map("disallowed_sales_channels")
  optoutMessage                         String?                  @map("optout_message")
  gupshupAccountStatus                  GupshupAccountStatus?    @map("gupshup_account_status")
  gupshupAccountStatusDetails           Json?                    @map("gupshup_account_status_reason")
  vndaShopHost                          String?                  @map("vnda_shop_host")
  vndaApiToken                          String?                  @map("vnda_api_token")
  isVndaActive                          Boolean                  @default(false) @map("is_vnda_active")
  millenniumApiHost                     String?                  @map("millennium_api_host")
  millenniumApiUser                     String?                  @map("millennium_api_user")
  millenniumApiPassword                 String?                  @map("millennium_api_password")
  isMillenniumActive                    Boolean                  @default(false) @map("is_millennium_active")
  disabledDefaultFields                 CustomersTableHeader[]   @map("disabled_default_fields")
  apiKeys                               ApiKey[]
  audienceRecommendations               AudienceRecommendation[]
  automaticReplies                      AutomaticReply[]
  automaticSortingOptions               AutomaticSortingOption[]
  automations                           Automation[]
  categories                            Category[]
  columnMappingConfigs                  ColumnMappingConfig[]
  initialContactFlow                    Flow?                    @relation("InitialContactFlow", fields: [initialContactFlowId], references: [id])
  companyDefinedFields                  CompanyDefinedField[]
  conversationCategories                ConversationCategory[]
  conversationSectors                   ConversationSector[]
  conversations                         Conversation[]
  customers                             Customer[]
  filters                               Filter[]
  flowTriggers                          FlowTrigger[]
  flows                                 Flow[]
  gupshupBillingEvents                  GupshupBillingEvent[]
  logs                                  Log[]
  messageTemplates                      MessageTemplate[]
  orders                                Order[]
  orderStatusHistory                    OrderStatusHistory[]
  products                              OrderProduct[]
  quickReplies                          QuickReply[]
  shortUrls                             ShortUrl[]
  smsCampaigns                          SmsCampaign[]
  tags                                  Tag[]
  employees                             User[]
  whatsappCampaigns                     WhatsappCampaign[]
  flowEvents                            FlowEvents[]
  gupshupDailyAppUsages                 GupshupDailyAppUsage[]
  invoices                              Invoice[]
  billingSettings                       BillingSettings?
  billingSettingsHistory                BillingSettingsHistory[]
  emailTemplates                        EmailTemplate[]
  emailDomains                          EmailDomain[]
  emailCampaigns                        EmailCampaign[]
  Role                                  Role[]
  CashbackConfig                        CashbackConfig[]
  Coupon                                Coupon[]
  CustomerCoupon                        CustomerCoupon[]
  Faq                                   Faq[]
  Product                               Product[]
  ProductCatalog                        ProductCatalog[]
  IntegrationConfig                     IntegrationConfig[]
  announcements                         Announcement[]
  webPushSubscriptions                  WebPushSubscription[]
  apiRequestsLogs                       ApiRequestsLogs[]
  Cart                                  Cart[]

  @@index([gupshupAppName])
  @@map("companies")
}

model CashbackConfig {
  createdAt                   DateTime          @default(now()) @map("created_at")
  updatedAt                   DateTime          @updatedAt @map("updated_at")
  id                          String            @id @default(uuid())
  companyId                   String            @map("company_id")
  discountValue               Int               @map("discount_value")
  discountType                DiscountType      @map("discount_type")
  daysToExpire                Int               @map("days_to_expire")
  cumulative                  Boolean           @default(false)
  minOrderValue               Int               @map("min_order_value")
  maxOrderValue               Int               @map("max_order_value")
  integration                 SourceIntegration @map("integration")
  creationMessageTemplateId   String            @map("creation_message_template_id")
  reminderMessageTemplateId   String?           @map("reminder_message_template_id")
  lastDayMessageTemplateId    String            @map("last_day_message_template_id")
  reminderDays                Int               @map("reminder_days")
  isReminderEnabled           Boolean           @default(true) @map("is_reminder_enabled")
  statusTrigger               String            @map("status_trigger")
  minValueToUseCoupon         Int?              @map("min_value_to_use_coupon")
  dailyMessageLimitOnWhatsapp Int               @default(100) @map("daily_message_limit_on_whatsapp")
  isActive                    Boolean           @default(true) @map("is_active")

  coupons          Coupon[]
  company          Company          @relation(fields: [companyId], references: [id])
  creationTemplate MessageTemplate  @relation(name: "CreationTemplateRelation", fields: [creationMessageTemplateId], references: [id])
  reminderTemplate MessageTemplate? @relation(name: "ReminderTemplateRelation", fields: [reminderMessageTemplateId], references: [id])
  lastDayTemplate  MessageTemplate  @relation(name: "LastDayTemplateRelation", fields: [lastDayMessageTemplateId], references: [id])

  @@unique([companyId, integration])
  @@index([companyId, integration, isActive])
  @@map("cashback_configs")
}

model Coupon {
  createdAt             DateTime     @default(now()) @map("created_at")
  updatedAt             DateTime     @default(now()) @updatedAt @map("updated_at")
  id                    String       @id @default(uuid())
  sourceId              String?      @map("source_id")
  companyId             String       @map("company_id")
  code                  String       @map("code")
  discountValue         Int          @map("discount_value")
  discountType          DiscountType @map("discount_type")
  usageLimit            Int?         @map("usage_limit")
  usageLimitPerCustomer Int?         @map("usage_limit_per_customer")
  startsAt              DateTime?    @map("starts_at")
  endsAt                DateTime?    @map("ends_at")
  type                  CouponType   @default(cashback) @map("type")
  minValueToUseCoupon   Int?         @map("min_value_to_use_coupon")
  isActive              Boolean      @default(true) @map("is_active")
  cashbackConfigId      String       @map("cashback_config_id")
  generatedByOrderId    String       @map("generated_by_order_id")

  customerCoupons CustomerCoupon[]
  order           Order?           @relation("GeneratedByOrder", fields: [generatedByOrderId], references: [id])
  cashbackConfig  CashbackConfig   @relation(fields: [cashbackConfigId], references: [id])
  company         Company          @relation(fields: [companyId], references: [id])

  @@index([generatedByOrderId])
  @@map("coupons")
}

model CustomerCoupon {
  id                String     @id @default(uuid())
  createdAt         DateTime   @default(now()) @map("created_at")
  updatedAt         DateTime   @default(now()) @updatedAt @map("updated_at")
  companyId         String     @map("company_id")
  couponId          String     @map("coupon_id")
  customerId        String     @map("customer_id")
  couponStep        CouponStep @default(pending) @map("coupon_step")
  scheduledSendTime DateTime?  @map("scheduled_send_time")
  isActive          Boolean    @default(true) @map("is_active")
  isUsed            Boolean    @default(false) @map("is_used")
  usedAt            DateTime?  @map("used_at")

  coupon   Coupon    @relation(fields: [couponId], references: [id])
  customer Customer  @relation(fields: [customerId], references: [id])
  messages Message[]
  company  Company   @relation(fields: [companyId], references: [id])

  @@unique([couponId, customerId])
  @@index([scheduledSendTime, isActive, companyId])
  @@map("customer_coupons")
}

model BillingSettings {
  createdAt                        DateTime                 @default(now()) @map("created_at")
  updatedAt                        DateTime                 @default(now()) @updatedAt @map("updated_at")
  id                               String                   @id @default(uuid())
  companyId                        String                   @unique @map("company_id")
  customerServiceFee               Int                      @map("customer_service_fee")
  platformFee                      Int                      @map("platform_fee")
  whatsappMarketingPackageLimit    Int?                     @map("whatsapp_marketing_package_limit")
  whatsappUtilityPackageLimit      Int?                     @map("whatsapp_utility_package_limit")
  whatsappServicePackageLimit      Int?                     @map("whatsapp_service_package_limit")
  whatsappMarketingMessageFee      Int?                     @map("whatsapp_marketing_message_cost")
  whatsappUtilityMessageFee        Int?                     @map("whatsapp_utility_message_cost")
  whatsappServiceMessageFee        Int?                     @map("whatsapp_service_message_cost")
  whatsappMarketingExtraMessageFee Int?                     @map("whatsapp_marketing_extra_message_fee")
  whatsappUtilityExtraMessageFee   Int?                     @map("whatsapp_utility_extra_message_fee")
  whatsappServiceExtraMessageFee   Int?                     @map("whatsapp_service_extra_message_fee")
  billingContactEmail              String?                  @map("billing_contact_email")
  paymentMethod                    PaymentMethod            @default(boleto) @map("payment_method")
  smsMessageFee                    Int?                     @map("sms_message_cost")
  smsPackageLimit                  Int?                     @map("sms_package_limit")
  smsExtraMessageFee               Int?                     @map("sms_extra_message_fee")
  company                          Company                  @relation(fields: [companyId], references: [id])
  BillingSettingsHistory           BillingSettingsHistory[]

  @@map("billing_settings")
}

model BillingSettingsHistory {
  createdAt                        DateTime        @default(now()) @map("created_at")
  updatedAt                        DateTime        @default(now()) @updatedAt @map("updated_at")
  id                               String          @id @default(uuid())
  billingSettingsId                String          @map("billing_settings_id")
  companyId                        String          @map("company_id")
  sourceCreatedAt                  DateTime        @default(now()) @map("source_created_at")
  customerServiceFee               Int             @map("customer_service_fee")
  platformFee                      Int             @map("platform_fee")
  whatsappMarketingPackageLimit    Int?            @map("whatsapp_marketing_package_limit")
  whatsappUtilityPackageLimit      Int?            @map("whatsapp_utility_package_limit")
  whatsappServicePackageLimit      Int?            @map("whatsapp_service_package_limit")
  whatsappMarketingMessageFee      Int?            @map("whatsapp_marketing_message_cost")
  whatsappUtilityMessageFee        Int?            @map("whatsapp_utility_message_cost")
  whatsappServiceMessageFee        Int?            @map("whatsapp_service_message_cost")
  whatsappMarketingExtraMessageFee Int?            @map("whatsapp_marketing_extra_message_fee")
  whatsappUtilityExtraMessageFee   Int?            @map("whatsapp_utility_extra_message_fee")
  whatsappServiceExtraMessageFee   Int?            @map("whatsapp_service_extra_message_fee")
  billingContactEmail              String?         @map("billing_contact_email")
  paymentMethod                    PaymentMethod   @default(boleto) @map("payment_method")
  smsMessageFee                    Int?            @map("sms_message_cost")
  smsPackageLimit                  Int?            @map("sms_package_limit")
  smsExtraMessageFee               Int?            @map("sms_extra_message_fee")
  company                          Company         @relation(fields: [companyId], references: [id])
  billingSettings                  BillingSettings @relation(fields: [billingSettingsId], references: [id])

  @@map("billing_settings_history")
}

model Invoice {
  createdAt      DateTime      @default(now()) @map("created_at")
  updatedAt      DateTime      @default(now()) @updatedAt @map("updated_at")
  id             String        @id @default(uuid())
  companyId      String        @map("company_id")
  paymentMethod  PaymentMethod @default(boleto) @map("payment_method")
  value          Int           @map("value")
  dueDate        DateTime      @map("due_date")
  referenceMonth DateTime      @map("reference_month")
  company        Company       @relation(fields: [companyId], references: [id])
  invoiceItems   InvoiceItem[]

  @@map("invoices")
}

model InvoiceItem {
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @default(now()) @updatedAt @map("updated_at")
  id          String   @id @default(uuid())
  invoiceId   String   @map("invoice_id")
  name        String   @map("name")
  unitPrice   Int      @map("unit_price")
  quantity    Int      @map("quantity")
  totalPrice  Int      @map("total_price")
  discount    Int      @map("discount")
  description String   @map("description")
  invoice     Invoice  @relation(fields: [invoiceId], references: [id])

  @@map("invoice_items")
}

model GupshupDailyAppUsage {
  id                          String   @id @default(uuid())
  appId                       String   @map("app_id")
  appName                     String   @map("app_name")
  authentication              Int      @map("authentication")
  cumulativeBill              Float    @map("cumulative_bill")
  currency                    String   @map("currency")
  date                        DateTime @map("date")
  discount                    Float    @map("discount")
  fep                         Int      @map("fep")
  ftc                         Int      @map("ftc")
  gsCap                       Float    @map("gs_cap")
  gsFees                      Float    @map("gs_fees")
  incomingMsg                 Int      @map("incoming_msg")
  outgoingMsg                 Int      @map("outgoing_msg")
  outgoingMediaMsg            Int      @map("outgoing_media_msg")
  marketing                   Int      @map("marketing")
  service                     Int      @map("service")
  templateMsg                 Int      @map("template_msg")
  templateMediaMsg            Int      @map("template_media_msg")
  totalFees                   Float    @map("total_fees")
  totalMsg                    Int      @map("total_msg")
  utility                     Int      @map("utility")
  waFees                      Float    @map("wa_fees")
  internationalAuthentication Int      @map("international_authentication")
  company                     Company  @relation(fields: [appName], references: [gupshupAppName])

  @@map("gupshup_daily_app_usage")
}

model ApiKey {
  createdAt   DateTime          @default(now()) @map("created_at")
  updatedAt   DateTime          @default(now()) @updatedAt @map("updated_at")
  id          String            @id @default(uuid())
  key         String            @unique
  companyId   String            @map("company_id")
  name        String?
  isActive    Boolean           @default(true) @map("is_active")
  integration SourceIntegration @default(unknown)
  company     Company           @relation(fields: [companyId], references: [id])

  @@map("api_keys")
}

model AutomaticSortingOption {
  createdAt                      DateTime                        @default(now()) @map("created_at")
  updatedAt                      DateTime                        @default(now()) @updatedAt @map("updated_at")
  id                             String                          @id @default(uuid())
  firstMessage                   String?                         @map("first_message")
  fileId                         String?                         @map("file_id")
  companyId                      String                          @map("company_id")
  isActive                       Boolean                         @default(true) @map("is_active")
  conversationCategoryId         String                          @unique @map("conversation_category_id")
  pos                            Float                           @default(0)
  automaticSortingOptionMessages AutomaticSortingOptionMessage[]
  company                        Company                         @relation(fields: [companyId], references: [id])
  conversationCategory           ConversationCategory            @relation(fields: [conversationCategoryId], references: [id], onDelete: Cascade)
  file                           File?                           @relation(fields: [fileId], references: [id])

  @@map("automatic_sorting_options")
}

model AutomaticSortingOptionMessage {
  createdAt                DateTime               @default(now()) @map("created_at")
  updatedAt                DateTime               @default(now()) @updatedAt @map("updated_at")
  id                       String                 @id @default(uuid())
  text                     String?
  automaticSortingOptionId String                 @map("automatic_sorting_option_id")
  automaticSortingOption   AutomaticSortingOption @relation(fields: [automaticSortingOptionId], references: [id], onDelete: Cascade)
}

model AutomaticReply {
  createdAt                DateTime                 @default(now()) @map("created_at")
  updatedAt                DateTime                 @default(now()) @updatedAt @map("updated_at")
  id                       String                   @id @default(uuid())
  companyId                String                   @map("company_id")
  keyword                  String
  message                  String
  condition                AutomaticReplyCondition
  conversationCategoryId   String?                  @map("conversation_category_id")
  fileId                   String?                  @map("file_id")
  conversationTicketStatus ConversationTicketStatus @default(open) @map("conversation_ticket_status")
  company                  Company                  @relation(fields: [companyId], references: [id])
  conversationCategory     ConversationCategory?    @relation(fields: [conversationCategoryId], references: [id])
  file                     File?                    @relation(fields: [fileId], references: [id])

  @@unique([keyword, companyId])
  @@map("automatic_replies")
}

model Automation {
  createdAt                     DateTime           @default(now()) @map("created_at")
  updatedAt                     DateTime           @default(now()) @updatedAt @map("updated_at")
  id                            String             @id @default(uuid())
  companyId                     String             @map("company_id")
  name                          String
  isActive                      Boolean            @default(false) @map("is_active")
  data                          Json?              @map("data")
  messageTemplateId             String?            @map("message_template_id")
  emailTemplateId               String?            @map("email_template_id")
  flowId                        String?            @map("flow_id")
  automationTypeId              String             @map("automation_type_id")
  cronExpression                String?            @map("cron_expression")
  filterId                      String?            @map("filter_id")
  monthlyMessageLimitOnWhatsapp Int                @default(3000) @map("monthly_message_limit_on_whatsapp")
  dailyMessageLimitOnWhatsapp   Int                @default(100) @map("daily_message_limit_on_whatsapp")
  monthlyEmailLimit             Int?               @map("monthly_email_limit")
  dailyEmailLimit               Int?               @map("daily_email_limit")
  scheduledJobId                String?            @map("scheduled_job_id")
  nextExecutionAt               DateTime?          @map("next_execution_at")
  templateArgs                  Json?              @map("template_args")
  emailTemplateArgs             Json?              @map("email_template_args")
  action                        AutomationAction   @default(send_message_template) @map("action")
  automationType                AutomationType     @relation(fields: [automationTypeId], references: [id])
  company                       Company            @relation(fields: [companyId], references: [id])
  filter                        Filter?            @relation(fields: [filterId], references: [id])
  messageTemplate               MessageTemplate?   @relation(fields: [messageTemplateId], references: [id])
  emailTemplate                 EmailTemplate?     @relation(fields: [emailTemplateId], references: [id])
  flow                          Flow?              @relation(fields: [flowId], references: [id])
  messages                      Message[]
  whatsappCampaigns             WhatsappCampaign[]
  emails                        Email[]
  emailsCampaigns               EmailCampaign[]
  minDaysSinceLastCampaign      Int?               @map("min_days_since_last_campaign")
  isAutomationRepetitionAllowed Boolean            @default(false) @map("is_automation_repetition_allowed")

  @@map("automations")
}

model AutomationType {
  createdAt         DateTime           @default(now()) @map("created_at")
  updatedAt         DateTime           @default(now()) @updatedAt @map("updated_at")
  id                String             @id @default(uuid())
  name              String
  slug              AutomationTypeSlug
  automations       Automation[]
  sourceIntegration SourceIntegration? @map("source_integration")

  @@map("automation_types")
}

model Conversation {
  createdAt                DateTime                  @default(now()) @map("created_at")
  updatedAt                DateTime                  @default(now()) @updatedAt @map("updated_at")
  id                       String                    @id @default(uuid())
  recipientPhoneNumberId   String                    @map("recipient_phone_number_id")
  recipientName            String                    @map("recipient_name")
  categoryId               String?                   @map("category_id")
  companyId                String                    @map("company_id")
  customerId               String                    @unique @map("customer_id")
  currentFlowNodeId        String?                   @map("current_flow_node_id")
  conversationTickets      ConversationTicket[]
  category                 ConversationCategory?     @relation(fields: [categoryId], references: [id])
  company                  Company                   @relation(fields: [companyId], references: [id])
  currentFlowNode          FlowNode?                 @relation(fields: [currentFlowNodeId], references: [id])
  customer                 Customer                  @relation(fields: [customerId], references: [id])
  messages                 Message[]
  whatsappSessions         WhatsappSession[]
  flowEvents               FlowEvents[]
  FlowNodeScheduledActions FlowNodeScheduledAction[]

  @@unique([recipientPhoneNumberId, companyId])
  @@index([companyId])
  @@index([customerId])
  @@map("conversations")
}

model Customer {
  createdAt             DateTime           @default(now()) @map("created_at")
  updatedAt             DateTime           @default(now()) @updatedAt @map("updated_at")
  id                    String             @id @default(uuid())
  phoneNumberId         String?            @map("phone_number_id")
  name                  String             @map("name")
  email                 String?            @map("email")
  isOptedIn             Boolean            @default(false) @map("is_opted_in")
  companyId             String             @map("company_id")
  tags                  String?
  isOptedOut            Boolean            @default(false) @map("is_opted_out")
  emailState            CustomerEmailState @default(pending) @map("email_state")
  customFields          Json?
  sourceCustomerId      String?            @map("source_customer_id")
  source                SourceIntegration  @default(unknown) @map("source")
  sourceCreatedAt       DateTime?          @map("source_created_at")
  sourceUpdatedAt       DateTime?          @map("source_updated_at")
  birthDate             DateTime?          @map("birth_date")
  sourceId              String?            @map("source_id")
  sourceUserId          String?            @map("source_user_id")
  isOptedInToNewsletter Boolean            @default(false) @map("is_opted_in_to_newsletter")
  city                  String?            @map("city")
  state                 String?            @map("state")
  country               String?            @map("country")
  cpf                   String?            @map("cpf")
  isDeleted             Boolean            @default(false) @map("is_deleted")
  notes                 String?            @map("notes")
  defaultAgentId        String?            @map("default_agent_id")
  integrationConfigId   String?            @map("integrations_config_id")

  defaultAgent User? @relation(fields: [defaultAgentId], references: [id])

  campaignRecipients CampaignRecipient[]
  conversation       Conversation?
  customerTags       CustomerTag[]
  company            Company             @relation(fields: [companyId], references: [id])
  orders             Order[]
  customerCoupon     CustomerCoupon[]
  integrationConfig  IntegrationConfig?  @relation(fields: [integrationConfigId], references: [id])
  cart               Cart[]
  aiAgentChatInfo    AIAgentChatInfo[]

  @@unique([phoneNumberId, email, companyId])
  @@index([companyId, isOptedOut])
  @@index([city])
  @@index([state])
  @@index([isDeleted])
  @@index([defaultAgentId])
  @@index([emailState])
  @@map("customers")
}

model Tag {
  createdAt    DateTime      @default(now()) @map("created_at")
  updatedAt    DateTime      @default(now()) @updatedAt @map("updated_at")
  id           String        @id @default(uuid())
  name         String
  companyId    String        @map("company_id")
  customerTags CustomerTag[]
  company      Company       @relation(fields: [companyId], references: [id])
  isDeleted    Boolean       @default(false) @map("is_deleted")

  @@unique([name, companyId])
  @@map("tags")
}

model Product {
  id                  String            @id @default(uuid())
  name                String?           @default("")
  title               String?           @default("")
  description         String?           @default("")
  sourceId            String?           @map("source_id")
  source              SourceIntegration @default(unknown)
  sourceCreatedAt     DateTime?         @map("source_created_at")
  sourceUpdatedAt     DateTime?         @map("source_updated_at")
  status              String?
  metadata            Json?
  integrationConfigId String?           @map("integrations_config_id")

  companyId         String             @map("company_id")
  company           Company            @relation(fields: [companyId], references: [id])
  integrationConfig IntegrationConfig? @relation(fields: [integrationConfigId], references: [id])

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")

  variants ProductVariant[]

  @@index([companyId])
  @@map("products")
}

model ProductVariant {
  id              String    @id @default(uuid())
  sourceCreatedAt DateTime? @map("source_created_at")
  sourceUpdatedAt DateTime? @map("source_updated_at")
  productId       String    @map("product_id")
  status          String?
  name            String?   @default("")
  title           String?   @default("")
  description     String?   @default("")
  sku             String?
  sourceId        String?   @map("source_id")
  barcode         String?
  price           Int? // Preço total do produto
  salePrice       Int?      @map("sale_price") // Preço de venda (quando há descontos e etc)
  costPrice       Int?      @map("cost_price") // Preço de custo
  stockQuantity   Int?      @map("stock_quantity")
  weight          Int?
  width           Int?
  height          Int?
  length          Int?
  imageUrl        String?   @map("image_url")
  url             String?
  isKit           Boolean?  @default(false) @map("is_kit")

  variantCode    String? @map("variant_code")
  keywords       String? @default("")
  metadata       Json?
  specifications Json?

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")

  titleSearchVector       Unsupported("tsvector")? @map("title_search_vector")
  descriptionSearchVector Unsupported("tsvector")? @map("description_search_vector")
  keywordsSearchVector    Unsupported("tsvector")? @map("keywords_search_vector")

  product                Product                 @relation(fields: [productId], references: [id])
  ProductVariantKitItem  ProductVariantKitItem[]
  CartItem               CartItem[]
  catalogProductMappings CatalogProductMapping[]

  @@index([productId])
  @@index([productId, sourceId, sku])
  @@index([name])
  @@index([description])
  @@index([titleSearchVector], type: Gin)
  @@index([descriptionSearchVector], type: Gin)
  @@index([keywordsSearchVector], type: Gin)
  @@map("product_variants")
}

model ProductVariantKitItem {
  id               String  @id @default(uuid())
  productVariantId String  @map("product_variant_id")
  kitItemId        String? @map("kit_item_id")

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")

  productVariant ProductVariant @relation(fields: [productVariantId], references: [id])

  @@index([productVariantId])
  @@map("product_variant_kit_items")
}

model ProductCatalog {
  id        String  @id @default(uuid())
  companyId String  @map("company_id")
  company   Company @relation(fields: [companyId], references: [id])

  externalCatalogId String            @map("external_catalog_id")
  name              String
  isActive          Boolean           @default(false)

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")

  catalogProductMappings CatalogProductMapping[]

  @@index([companyId])
  @@index([externalCatalogId])
  @@map("product_catalogs")
}

model CatalogProductMapping {
  id        String         @id @default(uuid())
  catalogId String         @map("catalog_id")
  catalog   ProductCatalog @relation(fields: [catalogId], references: [id])

  externalProductId String @map("external_product_id")

  productVariantId String         @map("product_variant_id")
  productVariant   ProductVariant @relation(fields: [productVariantId], references: [id])

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")

  @@unique([catalogId, externalProductId])
  @@index([productVariantId])
  @@index([catalogId])
  @@map("catalog_product_mappings")
}

model CustomerTag {
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @default(now()) @updatedAt @map("updated_at")
  id         String   @id @default(uuid())
  customerId String   @map("customer_id")
  tagId      String   @map("tag_id")
  customer   Customer @relation(fields: [customerId], references: [id])
  tag        Tag      @relation(fields: [tagId], references: [id])

  @@unique([customerId, tagId])
  @@index([tagId])
  @@map("customer_tags")
}

model CompanyDefinedField {
  createdAt DateTime                    @default(now()) @map("created_at")
  updatedAt DateTime                    @default(now()) @updatedAt @map("updated_at")
  id        String                      @id @default(uuid())
  companyId String                      @map("company_id")
  table     CompanyDefinedFieldTable    @map("table")
  name      String                      @map("name")
  dataType  CompanyDefinedFieldDataType @map("data_type")
  isActive  Boolean                     @default(true) @map("is_active")
  company   Company                     @relation(fields: [companyId], references: [id])

  @@unique([companyId, table, name])
  @@map("company_defined_fields")
}

model ConversationTicket {
  createdAt                                    DateTime                 @default(now()) @map("created_at")
  updatedAt                                    DateTime                 @default(now()) @updatedAt @map("updated_at")
  id                                           String                   @id @default(uuid())
  conversationId                               String                   @map("conversation_id")
  finishedAt                                   DateTime?                @map("finished_at")
  score                                        Int?
  firstResponseAt                              DateTime?                @map("first_response_at")
  firstAgentResponseAt                         DateTime?                @map("first_agent_response_at")
  ownerId                                      String?                  @map("owner_id")
  agentId                                      String?                  @map("agent_id")
  status                                       ConversationTicketStatus @default(open)
  categoryId                                   String?                  @map("category_id")
  isWaitingForUserMessage                      Boolean                  @default(false) @map("is_waiting_for_user_message")
  isWaitingForCsatFlow                         Boolean                  @default(false) @map("is_waiting_for_csat_flow")
  category                                     ConversationCategory?    @relation(fields: [categoryId], references: [id])
  conversation                                 Conversation             @relation(fields: [conversationId], references: [id])
  owner                                        User?                    @relation("TicketOwner", fields: [ownerId], references: [id])
  agent                                        User?                    @relation("TicketAgent", fields: [agentId], references: [id])
  businessHoursFirstAgentResponseTimeInSeconds Int?                     @map("business_hours_first_agent_response_time_in_seconds")
  businessHoursDurationInSeconds               Int?                     @map("business_hours_duration_in_seconds")

  @@index([categoryId])
  @@index([agentId])
  @@index([conversationId])
  @@map("conversation_tickets")
}

model WhatsappSession {
  createdAt      DateTime     @default(now()) @map("created_at")
  updatedAt      DateTime     @default(now()) @updatedAt @map("updated_at")
  id             String       @id @default(uuid())
  conversationId String       @map("conversation_id")
  conversation   Conversation @relation(fields: [conversationId], references: [id])

  @@index([conversationId, createdAt(sort: Desc)])
  @@map("whatsapp_sessions")
}

model Message {
  createdAt              DateTime      @default(now()) @map("created_at")
  updatedAt              DateTime      @default(now()) @updatedAt @map("updated_at")
  id                     String        @id @default(uuid())
  text                   String
  senderPhoneNumberId    String        @map("sender_phone_number_id")
  recipientPhoneNumberId String        @map("recipient_phone_number_id")
  conversationId         String        @map("conversation_id")
  fromSystem             Boolean       @default(false) @map("from_system")
  status                 MessageStatus @default(enqueued)
  whatsappMessageId      String?       @unique @map("whatsapp_message_id")
  mediaUrl               String?       @map("media_url")
  mediaType              MediaType?    @map("media_type")
  fileKey                String?       @map("file_key")
  tempId                 String?
  messageTemplateId      String?       @map("message_template_id")
  whatsappCampaignId     String?       @map("whatsapp_campaign_id")
  wamId                  String?       @unique @map("wam_id")
  firstReplyId           String?       @unique @map("first_reply_id")
  contextMessageId       String?       @map("context_message_id")
  errorCode              String?       @map("error_code")
  errorMessage           String?       @map("error_message")
  automationId           String?       @map("automation_id")
  flowNodeId             String?       @map("flow_node_id")
  customerCouponId       String?       @map("customer_coupon_id")
  createdByUserId        String?       @map("created_by_user_id")

  gupshupBillingEvent GupshupBillingEvent[]
  automation          Automation?           @relation(fields: [automationId], references: [id])
  conversation        Conversation          @relation(fields: [conversationId], references: [id])
  file                File?                 @relation(fields: [fileKey], references: [key])
  context             Message?              @relation("ContextMessage", fields: [contextMessageId], references: [id])
  contextMessages     Message[]             @relation("ContextMessage")
  firstReply          Message?              @relation("MessageReply", fields: [firstReplyId], references: [id])
  repliedTo           Message?              @relation("MessageReply")
  messageTemplate     MessageTemplate?      @relation(fields: [messageTemplateId], references: [id])
  whatsappCampaign    WhatsappCampaign?     @relation(fields: [whatsappCampaignId], references: [id])
  createdByUser       User?                 @relation(fields: [createdByUserId], references: [id])
  customerCoupon      CustomerCoupon?       @relation(fields: [customerCouponId], references: [id])

  shortUrls      ShortUrl[]
  flowEvents     FlowEvents[]
  abandonedCarts AbandonedCart[]
  messageCards   MessageCard[]
  flowNode       FlowNode?       @relation(fields: [flowNodeId], references: [id])

  @@index([automationId])
  @@index([whatsappMessageId])
  @@index([wamId])
  @@index([whatsappCampaignId])
  @@index([messageTemplateId])
  @@index([conversationId, fromSystem, createdAt(sort: Desc)])
  @@index([contextMessageId])
  @@map("messages")
}

model MessageCard {
  createdAt             DateTime            @default(now()) @map("created_at")
  updatedAt             DateTime            @default(now()) @updatedAt @map("updated_at")
  id                    String              @id @default(uuid())
  text                  String
  cardIndex             Int                 @map("card_index")
  messageId             String              @map("message_id")
  messageTemplateCardId String              @map("message_template_card_id")
  message               Message             @relation(fields: [messageId], references: [id])
  messageTemplateCard   MessageTemplateCard @relation(fields: [messageTemplateCardId], references: [id])

  @@index([messageId])
  @@map("message_cards")
}

model MessageTemplateSuggestion {
  createdAt        DateTime          @default(now()) @map("created_at")
  updatedAt        DateTime          @default(now()) @updatedAt @map("updated_at")
  id               String            @id @default(uuid())
  templateText     String            @map("template_text")
  isActive         Boolean           @default(true) @map("is_active")
  name             String            @map("name")
  businessSector   BusinessSector?   @map("business_sector")
  messageTemplates MessageTemplate[]

  @@map("message_template_suggestions")
}

model MessageTemplate {
  createdAt                   DateTime                   @default(now()) @map("created_at")
  updatedAt                   DateTime                   @default(now()) @updatedAt @map("updated_at")
  id                          String                     @id @default(uuid())
  companyId                   String                     @map("company_id")
  mediaUrl                    String?                    @map("media_url")
  mediaType                   MediaType?                 @map("media_type")
  gupshupTemplateId           String?                    @map("gupshup_template_id")
  whatsappTemplateId          String?                    @map("whatsapp_template_id")
  templateText                String                     @map("template_text") @db.VarChar(1028)
  status                      MessageTemplateStatus      @default(pending)
  name                        String
  type                        MessageTemplateType        @default(INITIAL_MESSAGE)
  whatsappTemplateCategory    WhatsappTemplateCategory   @default(MARKETING) @map("whatsapp_template_category")
  ctaLink                     String?                    @map("cta_link")
  messageTemplateSuggestionId String?                    @map("message_template_suggestion_id")
  communicationChannel        CommunicationChannel       @default(whatsapp) @map("communication_channel")
  footerText                  String?                    @map("footer_text")
  isLimitedOffer              Boolean                    @default(false) @map("is_limited_offer")
  limitedOfferText            String?                    @map("limited_offer_text")
  limitedOfferHasExpiration   Boolean                    @default(false) @map("limited_offer_has_expiration")
  limitedOfferExpirationDate  DateTime?                  @map("limited_offer_expiration_date")
  automations                 Automation[]
  gupshupTemplate             GupshupTemplate?
  messageTemplateButtons      MessageTemplateButton[]
  creationCashbackConfigs     CashbackConfig[]           @relation(name: "CreationTemplateRelation")
  reminderCashbackConfigs     CashbackConfig[]           @relation(name: "ReminderTemplateRelation")
  lastDayCashbackConfigs      CashbackConfig[]           @relation(name: "LastDayTemplateRelation")
  //alterar
  company                     Company                    @relation(fields: [companyId], references: [id])
  messageTemplateSuggestion   MessageTemplateSuggestion? @relation(fields: [messageTemplateSuggestionId], references: [id])
  messages                    Message[]
  smsCampaigns                SmsCampaign[]
  smsMessages                 SmsMessage[]
  whatsappCampaigns           WhatsappCampaign[]
  isDeleted                   Boolean                    @default(false) @map("is_deleted")
  messageTemplateCards        MessageTemplateCard[]

  @@unique([companyId, name])
  @@map("message_templates")
}

model MessageTemplateCard {
  createdAt       DateTime        @default(now()) @map("created_at")
  updatedAt       DateTime        @default(now()) @updatedAt @map("updated_at")
  id              String          @id @default(uuid())
  headerType      CardHeaderType? @map("header_type")
  mediaUrl        String?         @map("media_url")
  whatsappMediaId String?         @map("whatsapp_media_id")
  fileId          String?         @map("file_id")
  body            String
  cardIndex       Int             @map("card_index")

  messageTemplates  MessageTemplate         @relation(fields: [messageTemplateId], references: [id])
  messageTemplateId String                  @map("message_template_id")
  buttons           MessageTemplateButton[]
  messageCards      MessageCard[]
  file              File?                   @relation(fields: [fileId], references: [id])

  @@index([messageTemplateId])
  @@map("message_template_cards")
}

model MessageTemplateButton {
  createdAt             DateTime          @default(now()) @map("created_at")
  updatedAt             DateTime          @default(now()) @updatedAt @map("updated_at")
  id                    String            @id @default(uuid())
  messageTemplateId     String?           @map("message_template_id")
  text                  String            @map("text")
  offerCode             String?           @map("offer_code")
  url                   String?           @map("url")
  type                  MessageButtonType @map("type")
  messageTemplateCardId String?           @map("message_template_card_id")
  index                 Int               @default(0)

  MessageTemplateCard MessageTemplateCard? @relation(fields: [messageTemplateCardId], references: [id])
  messageTemplate     MessageTemplate?     @relation(fields: [messageTemplateId], references: [id])
  FlowTrigger         FlowTrigger[]

  @@unique([messageTemplateId, text, type])
  @@map("message_template_buttons")
}

model EmailTemplate {
  createdAt      DateTime          @default(now()) @map("created_at")
  updatedAt      DateTime          @default(now()) @updatedAt @map("updated_at")
  id             String            @id @default(uuid())
  companyId      String            @map("company_id")
  name           String
  subject        String
  html           String
  text           String
  unlayerDesign  Json?             @map("unlayer_design")
  isDeleted      Boolean           @default(false) @map("is_deleted")
  type           EmailTemplateType @default(MARKETING)
  category       EmailCategory     @default(MARKETING)
  company        Company           @relation(fields: [companyId], references: [id])
  emailCampaigns EmailCampaign[]
  emails         Email[]
  automations    Automation[]

  @@unique([companyId, name])
  @@map("email_templates")
}

model EmailDomain {
  id                       String                        @id @default(uuid())
  createdAt                DateTime                      @default(now()) @map("created_at")
  updatedAt                DateTime                      @default(now()) @updatedAt @map("updated_at")
  companyId                String                        @map("company_id")
  domain                   String                        @unique
  address                  String                        @unique
  replyTo                  String?                       @map("reply_to")
  provider                 EmailProvider                 @default(sendgrid)
  externalId               String?                       @map("external_id")
  category                 EmailCategory
  mailFromDomain           String                        @map("mail_from_domain")
  verificationToken        String?                       @map("verification_token")
  domainVerificationStatus EmailDomainVerificationStatus @default(pending) @map("domain_verification_status")
  lastVerifiedAt           DateTime?                     @map("last_verified_at")
  dnsRecords               Json?                         @map("dns_records")
  configurationSet         String?                       @map("configuration_set")
  sendgridSubuserId        String?                       @map("sendgrid_subuser_id")
  isWarmedUp               Boolean                       @default(false) @map("is_warmed_up")
  customAwsAccessKeyId     String?                       @map("custom_aws_access_key_id")
  customAwsSecretAccessKey String?                       @map("custom_aws_secret_access_key")
  customAwsRegion          String?                       @map("custom_aws_region")

  company         Company          @relation(fields: [companyId], references: [id])
  sendgridSubuser SendgridSubuser? @relation(fields: [sendgridSubuserId], references: [id])

  @@unique([companyId, category])
  @@map("email_domains")
}

model SendgridSubuser {
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @default(now()) @updatedAt @map("updated_at")
  id               String   @id @default(uuid())
  externalId       Int      @map("external_id")
  username         String   @map("username")
  email            String   @map("email")
  password         String   @map("password")
  apiKey           String?  @map("api_key")
  externalApiKeyId String?  @map("api_key_id")

  sendgridSubuserDedicatedIps SendgridSubuserDedicatedIp[]
  emailDomains                EmailDomain[]

  @@map("sendgrid_subusers")
}

model SendgridSubuserDedicatedIp {
  createdAt             DateTime @default(now()) @map("created_at")
  updatedAt             DateTime @default(now()) @updatedAt @map("updated_at")
  id                    String   @id @default(uuid())
  sendgridSubuserId     String   @map("sendgrid_subuser_id")
  sendgridDedicatedIpId String   @map("sendgrid_dedicated_ip_id")

  sendgridSubuser     SendgridSubuser     @relation(fields: [sendgridSubuserId], references: [id])
  sendgridDedicatedIp SendgridDedicatedIp @relation(fields: [sendgridDedicatedIpId], references: [id])

  @@unique([sendgridSubuserId, sendgridDedicatedIpId])
  @@map("sendgrid_subuser_dedicated_ips")
}

model SendgridDedicatedIp {
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")
  id        String   @id @default(uuid())
  ip        String   @map("ip")

  sendgridSubuserDedicatedIps SendgridSubuserDedicatedIp[]

  @@map("sendgrid_dedicated_ips")
}

enum EmailDomainVerificationStatus {
  pending
  success
  failed
}

model Email {
  createdAt       DateTime       @default(now()) @map("created_at")
  updatedAt       DateTime       @default(now()) @updatedAt @map("updated_at")
  id              String         @id @default(uuid())
  companyId       String         @map("company_id")
  externalId      String?        @map("external_id")
  customerId      String         @map("customer_id")
  recipientEmail  String         @map("recipient_email")
  senderEmail     String         @map("sender_email")
  replyTo         String?        @map("reply_to")
  emailTemplateId String         @map("email_template_id")
  subject         String
  html            String
  text            String
  status          EmailStatus    @default(sent)
  provider        EmailProvider  @default(sendgrid)
  firstOpenedAt   DateTime?      @map("first_opened_at")
  failureReason   String?        @map("failure_reason")
  emailCampaignId String?        @map("email_campaign_id")
  automationId    String?        @map("automation_id")
  unsubscribeKey  String?        @map("unsubscribe_key")
  emailCampaign   EmailCampaign? @relation(fields: [emailCampaignId], references: [id], onDelete: Cascade)
  emailTemplate   EmailTemplate  @relation(fields: [emailTemplateId], references: [id])
  automation      Automation?    @relation(fields: [automationId], references: [id])
  shortUrls       ShortUrl[]

  @@unique([externalId, provider])
  @@unique([unsubscribeKey])
  @@index([emailCampaignId])
  @@index([customerId, createdAt(sort: Desc)])
  @@map("emails")
}

model Order {
  createdAt                 DateTime          @default(now()) @map("created_at")
  updatedAt                 DateTime          @default(now()) @updatedAt @map("updated_at")
  id                        String            @id @default(uuid())
  source                    SourceIntegration @default(unknown) @map("source")
  sourceType                OrderSourceType?  @map("source_type")
  sourceId                  String            @map("source_id")
  sourceCreatedAt           DateTime          @map("source_created_at")
  sourceUpdatedAt           DateTime?         @map("source_updated_at")
  platformOrderId           String?           @map("platform_order_id")
  platformOrderSource       String?           @map("platform_order_source")
  value                     Int
  totalItemsValue           Int?              @map("total_items_value")
  totalItemsQuantity        Int?              @map("total_items_quantity")
  status                    String?
  companyId                 String            @map("company_id")
  customerId                String            @map("customer_id")
  coupon                    String?
  totalDiscountsValue       Int?              @map("total_discounts_value")
  totalShippingValue        Int?              @map("total_shipping_value")
  totalTaxValue             Int?              @map("total_tax_value")
  shippingCarrier           String?           @map("shipping_carrier")
  shipmentStatus            String?           @map("shipment_status")
  trackingCode              String?           @map("tracking_code")
  trackingUrl               String?           @map("tracking_url")
  isTrackingCodeSent        Boolean?          @default(false) @map("is_tracking_code_sent")
  isTrackingCodeSentByEmail Boolean?          @default(false) @map("is_tracking_code_sent_by_email")
  salesChannel              String?           @map("sales_channel")
  storeName                 String?           @map("store_name")
  userAgent                 String?           @map("user_agent")
  note                      String?           @map("note")
  noteAtributes             Json?             @map("note_attributes")
  currency                  String?
  statusUrl                 String?           @map("status_url")
  marketingData             Json?             @map("marketing_data")
  integrationConfigId       String?           @map("integrations_config_id")
  cancelReason              String?           @map("cancel_reason")
  cancelledAt               DateTime?         @map("cancelled_at")
  isSubscription            Boolean?          @default(false) @map("is_subscription")
  paymentMethods            String?           @map("payment_methods")

  orderItems         OrderItem[]
  orderStatusHistory OrderStatusHistory[]
  coupons            Coupon[]             @relation("GeneratedByOrder")

  company           Company            @relation(fields: [companyId], references: [id])
  customer          Customer           @relation(fields: [customerId], references: [id])
  integrationConfig IntegrationConfig? @relation(fields: [integrationConfigId], references: [id])

  @@unique([companyId, sourceId, source])
  @@index([companyId, customerId])
  @@index([companyId, customerId, sourceCreatedAt])
  @@index([companyId, status])
  @@index([storeName])
  @@index([platformOrderSource])
  @@index([salesChannel])
  @@index([coupon])
  @@map("orders")
}

model OrderStatusHistory {
  createdAt       DateTime  @default(now()) @map("created_at")
  updatedAt       DateTime  @default(now()) @updatedAt @map("updated_at")
  id              String    @id @default(uuid())
  orderId         String    @map("order_id")
  companyId       String    @map("company_id")
  sourceUpdatedAt DateTime? @map("source_updated_at")
  previousStatus  String?   @map("previous_status")
  newStatus       String    @map("new_status")
  isWhatsappSent  Boolean   @default(false) @map("is_whatsapp_sent")
  isEmailSent     Boolean   @default(false) @map("is_email_sent")

  order   Order   @relation(fields: [orderId], references: [id])
  company Company @relation(fields: [companyId], references: [id])

  @@unique([orderId, newStatus])
  @@index([orderId, newStatus])
  @@map("order_status_history")
}

model OrderItem {
  createdAt DateTime      @default(now()) @map("created_at")
  updatedAt DateTime      @default(now()) @updatedAt @map("updated_at")
  id        String        @id @default(uuid())
  quantity  Int?
  productId String?       @map("product_id")
  orderId   String        @map("order_id")
  order     Order         @relation(fields: [orderId], references: [id])
  product   OrderProduct? @relation(fields: [productId], references: [id])

  @@index([orderId, productId])
  @@index([productId])
  @@map("order_items")
}

model OrderProduct {
  createdAt         DateTime               @default(now()) @map("created_at")
  updatedAt         DateTime               @default(now()) @updatedAt @map("updated_at")
  id                String                 @id @default(uuid())
  sourceId          String?                @map("source_id")
  name              String
  brand             String?
  companyId         String                 @map("company_id")
  sku               String?
  orderItems        OrderItem[]
  productCategories OrderProductCategory[]
  company           Company                @relation(fields: [companyId], references: [id])

  @@index([companyId])
  @@map("order_products")
}

model OrderProductCategory {
  createdAt  DateTime     @default(now()) @map("created_at")
  updatedAt  DateTime     @default(now()) @updatedAt @map("updated_at")
  id         String       @id @default(uuid())
  productId  String       @map("product_id")
  categoryId String       @map("category_id")
  category   Category     @relation(fields: [categoryId], references: [id])
  product    OrderProduct @relation(fields: [productId], references: [id])

  @@map("order_product_categories")
}

model Category {
  createdAt         DateTime               @default(now()) @map("created_at")
  updatedAt         DateTime               @default(now()) @updatedAt @map("updated_at")
  id                String                 @id @default(uuid())
  sourceId          String?                @map("source_id")
  name              String?
  companyId         String                 @map("company_id")
  company           Company                @relation(fields: [companyId], references: [id])
  productCategories OrderProductCategory[]

  @@unique([companyId, name])
  @@map("categories")
}

model AbandonedCart {
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")
  id        String   @id @default(uuid())

  sourceCreatedAt     DateTime            @map("source_created_at")
  sourceId            String?             @map("source_id")
  sourceOrderId       String?             @map("source_order_id")
  customerPhoneNumber String?             @map("customer_phone_number_id")
  customerEmail       String?             @map("customer_email")
  customerName        String?             @map("customer_name")
  phoneNumberId       String?             @map("phone_number_id")
  companyId           String              @map("company_id")
  source              SourceIntegration   @default(unknown) @map("source")
  status              AbandonedCartStatus @default(abandoned) @map("status")
  recoveryMessageId   String?             @map("recovery_message_id")
  recoveryMessage     Message?            @relation(fields: [recoveryMessageId], references: [id])
  value               Int?
  cartUrl             String?             @map("cart_url")
  errorMessage        String?             @map("error_message")
  scheduledSendTime   DateTime?           @map("scheduled_send_time")
  products            Json?
  integrationConfigId String?             @map("integrations_config_id")

  integrationConfig IntegrationConfig? @relation(fields: [integrationConfigId], references: [id])

  @@unique([companyId, sourceId, source])
  @@map("abandoned_carts")
}

model GupshupTemplate {
  createdAt         DateTime                 @default(now()) @map("created_at")
  updatedAt         DateTime                 @default(now()) @updatedAt @map("updated_at")
  id                String                   @id
  messageTemplateId String                   @unique @map("message_template_id")
  elementName       String                   @map("element_name")
  languageCode      String                   @map("language_code")
  category          WhatsappTemplateCategory
  templateType      GupshupTemplateType
  content           String                   @map("content")
  buttons           Json?                    @map("buttons")
  example           String
  vertical          String
  header            String?
  footer            String?
  enableSample      Boolean                  @map("enable_sample")
  exampleHeader     String?                  @map("example_header")
  messageTemplate   MessageTemplate          @relation(fields: [messageTemplateId], references: [id])
  gupshupCards      Json?                    @map("gupshup_cards")

  @@map("gupshup_templates")
}

model ShortUrl {
  createdAt      DateTime        @default(now()) @map("created_at")
  updatedAt      DateTime        @default(now()) @updatedAt @map("updated_at")
  id             String          @id @default(uuid())
  numericId      Int?            @unique @default(autoincrement()) @map("numeric_id")
  shortId        String?         @map("short_id")
  messageId      String?         @map("message_id")
  companyId      String          @map("company_id")
  originalUrl    String?         @map("original_url")
  smsMessageId   String?         @map("sms_message_id")
  emailId        String?         @map("email_id")
  shortUrlClicks ShortUrlClick[]
  company        Company         @relation(fields: [companyId], references: [id])
  message        Message?        @relation(fields: [messageId], references: [id])
  smsMessage     SmsMessage?     @relation(fields: [smsMessageId], references: [id])
  email          Email?          @relation(fields: [emailId], references: [id])

  @@index([messageId])
  @@index([smsMessageId])
  @@map("short_urls")
}

model ShortUrlClick {
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @default(now()) @updatedAt @map("updated_at")
  id         String   @id @default(uuid())
  shortUrlId String   @map("short_url_id")
  userAgent  String?  @map("user_agent")
  shortUrl   ShortUrl @relation(fields: [shortUrlId], references: [id])

  @@index([shortUrlId])
  @@map("short_url_clicks")
}

model ConversationCategory {
  createdAt                       DateTime                @default(now()) @map("created_at")
  updatedAt                       DateTime                @default(now()) @updatedAt @map("updated_at")
  id                              String                  @id @default(uuid())
  name                            String
  pos                             Float                   @default(0)
  isDeleted                       Boolean                 @default(false) @map("is_deleted")
  companyId                       String                  @map("company_id")
  automaticSortingOptionMessageId String?
  automaticReplies                AutomaticReply[]
  automaticSortingOption          AutomaticSortingOption?
  company                         Company                 @relation(fields: [companyId], references: [id])
  conversationTickets             ConversationTicket[]
  conversations                   Conversation[]
  conversationSectorId            String?                 @map("conversation_sector_id")
  conversationSector              ConversationSector?     @relation(fields: [conversationSectorId], references: [id])

  @@unique([companyId, name])
  @@map("conversation_categories")
}

model ConversationSector {
  id                      String                   @id @default(uuid())
  name                    String
  position                Float                    @default(0)
  isDeleted               Boolean                  @default(false) @map("is_deleted")
  companyId               String                   @map("company_id")
  company                 Company                  @relation(fields: [companyId], references: [id])
  categories              ConversationCategory[]
  userConversationSectors UserConversationSector[]

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")

  @@unique([companyId, name])
  @@map("conversation_sectors")
}

model WhatsappCampaign {
  createdAt              DateTime               @default(now()) @map("created_at")
  updatedAt              DateTime               @default(now()) @updatedAt @map("updated_at")
  id                     String                 @id @default(uuid())
  companyId              String                 @map("company_id")
  templateId             String                 @map("message_template_id")
  totalRecipients        Int                    @map("total_recipients")
  status                 WhatsappCampaignStatus @default(in_progress)
  totalProcessed         Int                    @default(0) @map("total_processed")
  filterCriteria         String?                @map("filter_criteria")
  createdByUserId        String?                @map("created_by_user_id")
  scheduledExecutionTime DateTime?              @map("scheduled_execution_time")
  templateArgs           Json?                  @map("template_args")
  scheduledJobId         String?                @map("scheduled_job_id")
  errorMessage           String?                @map("error_message")
  automationId           String?                @map("automation_id")
  campaignExperimentId   String?                @map("campaign_experiment_id")
  campaignExperiment     CampaignExperiment?    @relation(fields: [campaignExperimentId], references: [id])
  type                   CampaignType           @default(standard)
  campaignRecipients     CampaignRecipient[]
  messages               Message[]
  automation             Automation?            @relation(fields: [automationId], references: [id])
  company                Company                @relation(fields: [companyId], references: [id])
  createdByUser          User?                  @relation(fields: [createdByUserId], references: [id])
  template               MessageTemplate        @relation(fields: [templateId], references: [id])

  @@index([companyId])
  @@map("whatsapp_campaigns")
}

model EmailCampaign {
  createdAt              DateTime             @default(now()) @map("created_at")
  updatedAt              DateTime             @default(now()) @updatedAt @map("updated_at")
  id                     String               @id @default(uuid())
  companyId              String               @map("company_id")
  emailTemplateId        String               @map("email_template_id")
  totalRecipients        Int                  @map("total_recipients")
  totalProcessed         Int                  @default(0) @map("total_processed")
  filterCriteria         String?              @map("filter_criteria")
  status                 EmailCampaignStatus  @default(scheduled)
  createdByUserId        String?              @map("created_by_user_id")
  scheduledExecutionTime DateTime?            @map("scheduled_execution_time")
  scheduledJobId         String?              @map("scheduled_job_id")
  errorMessage           String?              @map("error_message")
  templateArgs           Json?                @map("template_args")
  automationId           String?              @map("automation_id")
  automation             Automation?          @relation(fields: [automationId], references: [id])
  campaignRecipients     CampaignRecipient[]
  emails                 Email[]
  company                Company              @relation(fields: [companyId], references: [id])
  createdByUser          User?                @relation(fields: [createdByUserId], references: [id])
  emailTemplate          EmailTemplate        @relation(fields: [emailTemplateId], references: [id])
  emailCampaignResult    EmailCampaignResult?

  @@index([companyId])
  @@map("email_campaigns")
}

model EmailCampaignResult {
  createdAt         DateTime      @default(now()) @map("created_at")
  updatedAt         DateTime      @default(now()) @updatedAt @map("updated_at")
  id                String        @id @default(uuid())
  emailCampaignId   String        @unique @map("email_campaign_id")
  totalRecipients   Int           @default(0) @map("total_recipients")
  totalSent         Int           @default(0) @map("total_sent")
  totalFailures     Int           @default(0) @map("total_failures")
  totalDelivered    Int           @default(0) @map("total_delivered")
  totalReads        Int           @default(0) @map("total_reads")
  totalUniqueClicks Int           @default(0) @map("total_unique_clicks")
  totalOpens        Int           @default(0) @map("total_opens")
  totalClicks       Int           @default(0) @map("total_clicks")
  totalBounces      Int           @default(0) @map("total_bounces")
  totalUnsubscribes Int           @default(0) @map("total_unsubscribes")
  totalSpamReports  Int           @default(0) @map("total_spam_reports")
  emailCampaign     EmailCampaign @relation(fields: [emailCampaignId], references: [id])

  @@index([emailCampaignId])
  @@map("email_campaign_results")
}

model CampaignRecipient {
  createdAt          DateTime @default(now()) @map("created_at")
  updatedAt          DateTime @default(now()) @updatedAt @map("updated_at")
  id                 String   @id @default(uuid())
  whatsappCampaignId String?  @map("whatsapp_campaign_id")
  customerId         String   @map("customer_id")
  smsCampaignId      String?  @map("sms_campaign_id")
  emailCampaignId    String?  @map("email_campaign_id")

  campaignExperimentId String?             @map("campaign_experiment_id")
  type                 CampaignType        @default(standard)
  customer             Customer            @relation(fields: [customerId], references: [id])
  smsCampaign          SmsCampaign?        @relation(fields: [smsCampaignId], references: [id])
  whatsappCampaign     WhatsappCampaign?   @relation(fields: [whatsappCampaignId], references: [id])
  emailCampaign        EmailCampaign?      @relation(fields: [emailCampaignId], references: [id])
  campaignExperiment   CampaignExperiment? @relation(fields: [campaignExperimentId], references: [id])

  @@unique([emailCampaignId, customerId])
  @@unique([whatsappCampaignId, customerId])
  @@unique([smsCampaignId, customerId])
  @@map("campaign_recipients")
}

model Socket {
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")
  id        String   @unique
  userId    String   @map("user_id")
  user      User     @relation(fields: [userId], references: [id])

  @@map("sockets")
}

model File {
  createdAt               DateTime                 @default(now()) @map("created_at")
  updatedAt               DateTime                 @default(now()) @updatedAt @map("updated_at")
  id                      String                   @unique @default(uuid())
  name                    String
  mimeType                String
  mediaType               MediaType?
  size                    Int
  key                     String                   @unique
  publicUrl               String?                  @map("public_url")
  whatsappMediaId         String?                  @map("whatsapp_media_id")
  automaticReplies        AutomaticReply[]
  automaticSortingOptions AutomaticSortingOption[]
  messages                Message[]
  messageTemplateCard     MessageTemplateCard[]

  @@map("files")
}

model SmsCampaign {
  createdAt              DateTime            @default(now()) @map("created_at")
  updatedAt              DateTime            @default(now()) @updatedAt @map("updated_at")
  id                     String              @id @default(uuid())
  companyId              String              @map("company_id")
  messageTemplateId      String              @map("message_template_id")
  totalRecipients        Int                 @map("total_recipients")
  totalProcessed         Int                 @default(0) @map("total_processed")
  filterCriteria         String?             @map("filter_criteria")
  templateArgs           Json?               @map("template_args")
  scheduledExecutionTime DateTime?           @map("scheduled_execution_time")
  scheduledJobId         String?             @map("scheduled_job_id")
  createdByUserId        String?             @map("created_by_user_id")
  errorMessage           String?             @map("error_message")
  status                 SmsCampaignStatus   @default(in_progress)
  campaignRecipients     CampaignRecipient[]
  company                Company             @relation(fields: [companyId], references: [id])
  createdByUser          User?               @relation(fields: [createdByUserId], references: [id])
  template               MessageTemplate     @relation(fields: [messageTemplateId], references: [id])
  messages               SmsMessage[]

  @@index([companyId])
  @@map("sms_campaigns")
}

model SmsMessage {
  createdAt              DateTime        @default(now()) @map("created_at")
  updatedAt              DateTime        @default(now()) @updatedAt @map("updated_at")
  id                     String          @id @default(uuid())
  text                   String
  smsCampaignId          String?         @map("sms_campaign_id")
  recipientPhoneNumberId String
  messageTemplateId      String          @map("message_template_id")
  status                 MessageStatus   @default(enqueued)
  smsId                  String?         @map("sms_id")
  errorMessage           String?         @map("error_message")
  shortUrl               ShortUrl[]
  messageTemplate        MessageTemplate @relation(fields: [messageTemplateId], references: [id])
  smsCampaign            SmsCampaign?    @relation(fields: [smsCampaignId], references: [id])

  @@index([smsCampaignId, status])
  @@index([smsCampaignId, createdAt])
  @@map("sms_messages")
}

model Log {
  createdAt DateTime    @default(now()) @map("created_at")
  updatedAt DateTime    @default(now()) @updatedAt @map("updated_at")
  id        String      @id @default(uuid())
  message   String
  type      LogType?
  source    LogSource?
  meta      Json?
  companyId String?     @map("company_id")
  severity  LogSeverity
  company   Company?    @relation(fields: [companyId], references: [id])

  @@map("logs")
}

model ColumnMappingConfig {
  createdAt DateTime                 @default(now()) @map("created_at")
  updatedAt DateTime                 @default(now()) @updatedAt @map("updated_at")
  id        String                   @id @default(uuid())
  companyId String                   @map("company_id")
  table     ColumnMappingConfigTable
  mapping   Json
  company   Company                  @relation(fields: [companyId], references: [id])

  @@unique([companyId, table])
  @@map("column_mapping_configs")
}

model SocketIoAttachments {
  createdAt DateTime @default(now()) @map("created_at")
  id        String   @id @default(uuid())
  payload   Bytes

  @@map("socket_io_attachments")
}

model Filter {
  createdAt   DateTime     @default(now()) @map("created_at")
  updatedAt   DateTime     @default(now()) @updatedAt @map("updated_at")
  id          String       @id @default(uuid())
  companyId   String       @map("company_id")
  name        String       @map("name")
  type        FilterType   @map("type")
  criteria    String       @map("criteria")
  automations Automation[]
  company     Company      @relation(fields: [companyId], references: [id])

  @@unique([companyId, name])
  @@map("filters")
}

model AudienceRecommendation {
  createdAt      DateTime                    @default(now()) @map("created_at")
  updatedAt      DateTime                    @default(now()) @updatedAt @map("updated_at")
  id             String                      @id @default(uuid())
  companyId      String                      @map("company_id")
  classId        String                      @map("audience_recommendation_class_id")
  meta           Json?
  filterCriteria String                      @map("filter_criteria")
  class          AudienceRecommendationClass @relation(fields: [classId], references: [id])
  company        Company                     @relation(fields: [companyId], references: [id])

  @@map("audience_recommendations")
}

model AudienceRecommendationClass {
  createdAt               DateTime                        @default(now()) @map("created_at")
  updatedAt               DateTime                        @default(now()) @updatedAt @map("updated_at")
  id                      String                          @id @default(uuid())
  name                    String                          @unique @map("name")
  description             String                          @map("description")
  slug                    AudienceRecommendationClassSlug @unique @map("slug")
  audienceRecommendations AudienceRecommendation[]

  @@map("audience_recommendation_classes")
}

model PromptThread {
  createdAt           DateTime             @default(now()) @map("created_at")
  updatedAt           DateTime             @default(now()) @updatedAt @map("updated_at")
  id                  String               @unique @default(uuid())
  initialPromptId     String               @map("initial_prompt_id")
  userId              String?              @map("user_id")
  promptCompletitions PromptCompletition[]
  initialPrompt       InitialPrompt        @relation(fields: [initialPromptId], references: [id])
  user                User?                @relation(fields: [userId], references: [id])

  @@map("prompt_threads")
}

model PromptCompletition {
  createdAt          DateTime     @default(now()) @map("created_at")
  updatedAt          DateTime     @default(now()) @updatedAt @map("updated_at")
  id                 String       @unique @default(uuid())
  promptText         String       @map("prompt_text")
  promptTokens       Int?         @map("prompt_tokens")
  completitionTokens Int?         @map("completition_tokens")
  finishReason       String?      @map("finish_reason")
  completitionText   String?      @map("completition_text")
  promptThreadId     String       @map("prompt_thread_id")
  promptThread       PromptThread @relation(fields: [promptThreadId], references: [id])

  @@map("prompt_completitions")
}

model InitialPrompt {
  createdAt     DateTime          @default(now()) @map("created_at")
  updatedAt     DateTime          @default(now()) @updatedAt @map("updated_at")
  id            String            @id @default(uuid())
  type          InitialPromptType
  promptText    String            @map("prompt_text")
  promptThreads PromptThread[]

  @@map("initial_prompts")
}

model GupshupBillingEvent {
  createdAt                DateTime @default(now()) @map("created_at")
  updatedAt                DateTime @default(now()) @updatedAt @map("updated_at")
  id                       String   @id @default(uuid())
  gupshupAppName           String?  @map("gupshup_app_name")
  deductionsType           String?  @map("deduction_type")
  deductionsModel          String?  @map("deduction_model")
  deductionsSource         String?  @map("deduction_source")
  deductionsCategory       String?  @map("deduction_category")
  referencesId             String?  @map("reference_id")
  referencesGsId           String?  @map("reference_gs_id")
  referencesConversationId String?  @map("reference_conversation_id")
  referencesDestination    String?  @map("reference_destination")
  timestamp                DateTime @map("timestamp")
  deductionsBillable       Boolean? @map("deduction_billable")
  company                  Company? @relation(fields: [gupshupAppName], references: [gupshupAppName])
  message                  Message? @relation(fields: [referencesGsId], references: [whatsappMessageId])

  @@index([gupshupAppName])
  @@index([referencesGsId])
  @@index([deductionsBillable])
  @@index([deductionsModel, deductionsType])
  @@map("gupshup_billing_events")
}

model QuickReply {
  createdAt DateTime @default(now()) @map("created_at")
  id        String   @id @default(uuid())
  text      String
  title     String
  companyId String   @map("company_id")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")
  company   Company  @relation(fields: [companyId], references: [id])

  @@map("quick_replies")
}

model FlowEvents {
  id               String          @id @default(uuid())
  companyId        String          @map("company_id")
  conversationId   String          @map("conversation_id")
  flowId           String?         @map("flow_id")
  flowNodeId       String?         @map("flow_node_id")
  flowTriggerId    String?         @map("flow_trigger_id")
  flowNodeButtonId String?         @map("flow_node_button_id")
  messageId        String?         @map("message_id")
  metadata         Json?
  fromSystem       Boolean         @default(false) @map("from_system")
  flowNode         FlowNode?       @relation(fields: [flowNodeId], references: [id])
  flowNodeButton   FlowNodeButton? @relation(fields: [flowNodeButtonId], references: [id])
  flowTrigger      FlowTrigger?    @relation(fields: [flowTriggerId], references: [id])
  company          Company         @relation(fields: [companyId], references: [id])
  conversation     Conversation    @relation(fields: [conversationId], references: [id])
  flow             Flow?           @relation(fields: [flowId], references: [id])
  message          Message?        @relation(fields: [messageId], references: [id])
  createdAt        DateTime        @default(now()) @map("created_at")
  updatedAt        DateTime        @default(now()) @updatedAt @map("updated_at")

  @@index([companyId])
  @@index([conversationId])
  @@index([flowId])
  @@index([flowNodeId])
  @@index([flowTriggerId])
  @@index([messageId])
  @@map("flow_events")
}

model Flow {
  createdAt            DateTime      @default(now()) @map("created_at")
  updatedAt            DateTime      @default(now()) @updatedAt @map("updated_at")
  id                   String        @id @default(uuid())
  companyId            String        @map("company_id")
  isActive             Boolean       @default(false) @map("is_active")
  title                String
  type                 FlowType      @default(default)
  repeatOnInvalidInput Boolean       @default(false) @map("repeat_on_invalid_input")
  companies            Company[]     @relation("InitialContactFlow")
  flowNodes            FlowNode[]
  flowTriggers         FlowTrigger[]
  company              Company       @relation(fields: [companyId], references: [id])
  flowEvents           FlowEvents[]
  automations          Automation[]
  isDeleted            Boolean       @default(false) @map("is_deleted")

  @@map("flows")
}

model FlowNode {
  createdAt                DateTime                  @default(now()) @map("created_at")
  updatedAt                DateTime                  @default(now()) @updatedAt @map("updated_at")
  id                       String                    @id @default(uuid())
  type                     FlowNodeType
  data                     Json
  posX                     Int
  posY                     Int
  flowId                   String                    @map("flow_id")
  nextFlowNodeId           String?                   @map("next_flow_node_id")
  nextFlowNodeOnErrorId    String?                   @map("next_flow_node_on_error_id")
  conversation             Conversation[]
  flowNodeButtons          FlowNodeButton[]          @relation("FlowNodeButton")
  flowNodeConditionBlocks  FlowNodeConditionBlock[]  @relation("FlowNodeConditionBlock")
  triggerButtons           FlowNodeButton[]          @relation("FlowNodeButtonTrigger")
  triggerConditionBlocks   FlowNodeConditionBlock[]  @relation("FlowNodeConditionBlockTrigger")
  flow                     Flow                      @relation(fields: [flowId], references: [id])
  nextFlowNode             FlowNode?                 @relation("NextFlowNode", fields: [nextFlowNodeId], references: [id])
  nextFlowNodeOnError      FlowNode?                 @relation("NextFlowNodeOnError", fields: [nextFlowNodeOnErrorId], references: [id])
  flowNodes                FlowNode[]                @relation("NextFlowNode")
  flowTriggers             FlowTrigger[]
  flowEvents               FlowEvents[]
  flowNodeScheduledActions FlowNodeScheduledAction[]
  messages                 Message[]
  isDeleted                Boolean                   @default(false) @map("is_deleted")
  FlowNode                 FlowNode[]                @relation("NextFlowNodeOnError")

  @@map("flow_nodes")
}

model FlowTrigger {
  createdAt               DateTime               @default(now()) @map("created_at")
  updatedAt               DateTime               @default(now()) @updatedAt @map("updated_at")
  id                      String                 @id @default(uuid())
  type                    FlowTriggerType
  text                    String
  messageTemplateButtonId String?                @map("message_template_button_id")
  companyId               String                 @map("company_id")
  flowId                  String                 @map("flow_id")
  targetFlowNodeId        String                 @map("target_flow_node_id")
  invocationCount         Int                    @default(0) @map("invocation_count")
  isDefault               Boolean                @default(false) @map("is_default")
  company                 Company                @relation(fields: [companyId], references: [id])
  flow                    Flow                   @relation(fields: [flowId], references: [id])
  targetFlowNode          FlowNode               @relation(fields: [targetFlowNodeId], references: [id])
  flowEvents              FlowEvents[]
  messageTemplateButton   MessageTemplateButton? @relation(fields: [messageTemplateButtonId], references: [id])
  isDeleted               Boolean                @default(false) @map("is_deleted")

  @@unique([companyId, type, text, createdAt])
  @@map("flow_triggers")
}

model FlowNodeButton {
  createdAt        DateTime          @default(now()) @map("created_at")
  updatedAt        DateTime          @default(now()) @updatedAt @map("updated_at")
  id               String            @id @default(uuid())
  text             String
  url              String?
  flowNodeId       String            @map("flow_node_id")
  targetFlowNodeId String?           @map("target_flow_node_id")
  clickCount       Int               @default(0) @map("click_count")
  type             MessageButtonType
  index            Int
  flowNode         FlowNode          @relation("FlowNodeButton", fields: [flowNodeId], references: [id])
  targetFlowNode   FlowNode?         @relation("FlowNodeButtonTrigger", fields: [targetFlowNodeId], references: [id])
  flowEvents       FlowEvents[]
  isDeleted        Boolean           @default(false) @map("is_deleted")

  @@unique([flowNodeId, text, type])
  @@map("flow_node_buttons")
}

model FlowNodeConditionBlock {
  createdAt           DateTime            @default(now()) @map("created_at")
  updatedAt           DateTime            @default(now()) @updatedAt @map("updated_at")
  id                  String              @id @default(uuid())
  conditionalJoinType ConditionalJoinType @map("conditional_join_type")
  priority            Int
  targetFlowNodeId    String?             @map("target_flow_node_id")
  flowNodeId          String              @map("flow_node_id")
  targetFlowNode      FlowNode?           @relation("FlowNodeConditionBlockTrigger", fields: [targetFlowNodeId], references: [id])
  flowNode            FlowNode            @relation("FlowNodeConditionBlock", fields: [flowNodeId], references: [id])
  flowNodeConditions  FlowNodeCondition[] @relation("FlowNodeCondition")
  isDeleted           Boolean             @default(false) @map("is_deleted")

  @@map("flow_node_condition_blocks")
}

model FlowNodeScheduledAction {
  createdAt      DateTime                      @default(now()) @map("created_at")
  updatedAt      DateTime                      @default(now()) @updatedAt @map("updated_at")
  id             String                        @id @default(uuid())
  flowNodeId     String                        @map("flow_node_id")
  conversationId String                        @map("conversation_id")
  executionTime  DateTime                      @map("execution_time")
  status         FlowNodeScheduledActionStatus @default(scheduled)
  context        Json?                         @map("context")
  isDeleted      Boolean                       @default(false) @map("is_deleted")
  log            String?                       @map("log")
  flowNode       FlowNode                      @relation(fields: [flowNodeId], references: [id])
  conversation   Conversation                  @relation(fields: [conversationId], references: [id])

  @@index([executionTime])
  @@map("flow_node_scheduled_actions")
}

model FlowNodeCondition {
  createdAt                DateTime               @default(now()) @map("created_at")
  updatedAt                DateTime               @default(now()) @updatedAt @map("updated_at")
  id                       String                 @id @default(uuid())
  type                     flowNodeConditionsType
  value                    Json
  comparisonOperator       ComparisonOperator     @map("comparison_operator")
  flowNodeConditionBlockId String                 @map("flow_node_condition_block_id")
  flowNodeConditionBlock   FlowNodeConditionBlock @relation("FlowNodeCondition", fields: [flowNodeConditionBlockId], references: [id])
  isDeleted                Boolean                @default(false) @map("is_deleted")

  @@map("flow_node_conditions")
}

model CampaignExperiment {
  createdAt          DateTime      @default(now()) @map("created_at")
  updatedAt          DateTime      @default(now()) @updatedAt @map("updated_at")
  id                 String        @id @default(uuid())
  companyId          String        @map("company_id")
  winningMetric      WinningMetric @map("winning_metric")
  testSizePercentage Int           @map("test_size_percentage")
  scheduledStartTime DateTime      @map("scheduled_start_time")
  scheduledEndTime   DateTime      @map("scheduled_end_time")
  filterCriteria     String?       @map("filter_criteria")
  scheduledJobId     String?       @map("scheduled_job_id")

  campaignRecipients CampaignRecipient[]
  status             ExperimentStatus    @default(scheduled)
  whatsappCampaigns  WhatsappCampaign[]

  @@map("campaign_experiments")
}

model UserConversationSector {
  userId               String             @map("user_id")
  conversationSectorId String             @map("conversation_sector_id")
  user                 User               @relation(fields: [userId], references: [id])
  conversationSector   ConversationSector @relation(fields: [conversationSectorId], references: [id])

  @@unique([userId, conversationSectorId])
  @@map("user_conversation_sectors")
}

model Faq {
  createdAt    DateTime                 @default(now()) @map("created_at")
  updatedAt    DateTime                 @default(now()) @updatedAt @map("updated_at")
  id           String                   @id @default(uuid())
  question     String
  answer       String
  searchVector Unsupported("tsvector")? @map("search_vector")
  companyId    String                   @map("company_id")
  company      Company                  @relation(fields: [companyId], references: [id])

  @@index([searchVector], type: Gin)
  @@map("faq")
}

model IntegrationConfig {
  id                    String            @id @default(uuid())
  companyId             String
  source                SourceIntegration
  config                Json
  isOrderActive         Boolean           @default(false)
  isAbandonedCartActive Boolean           @default(false)
  isProductActive       Boolean           @default(false)
  isActive              Boolean           @default(true)
  description           String            @default("")
  createdAt             DateTime          @default(now()) @map("created_at")
  updatedAt             DateTime          @default(now()) @updatedAt @map("updated_at")

  company       Company         @relation(fields: [companyId], references: [id])
  Order         Order[]
  Customer      Customer[]
  AbandonedCart AbandonedCart[]
  Product       Product[]

  @@map("integrations_config")
}

model Announcement {
  id        String   @id @default(uuid())
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")
  title     String
  html      String
  companyId String?

  company Company? @relation(fields: [companyId], references: [id])

  @@map("announcements")
}

enum WinningMetric {
  engagement_rate
  read_rate
}

enum FlowTriggerType {
  exact_match
  keyword_match
  abandoned_cart
  quick_reply_message_template
  csat
  initial_contact
}

enum MessageButtonType {
  QUICK_REPLY
  URL
  COPY_CODE
}

enum FlowType {
  default
  abandoned_cart
  csat
  initial_contact
}

enum FlowNodeType {
  send_whatsapp_message
  trigger
  move_conversation_to_category
  send_whatsapp_media
  add_tag_to_customer
  end_whatsapp_conversation
  save_customer_response
  conditions_check
  time_delay
  send_whatsapp_message_template
  http_request
  send_email_template
  assign_conversation_ticket_to_agent
}

enum ConditionalJoinType {
  AND
  OR
}

enum flowNodeConditionsType {
  default
  total_orders
  total_purchases
  days_since_last_purchase
  has_tag
  abandoned_cart_value
}

enum ComparisonOperator {
  EQUALS        @map("=")
  GREATER_THAN  @map(">")
  LESS_THAN     @map("<")
  GREATER_EQUAL @map(">=")
  LESS_EQUAL    @map("<=")
  IN            @map("IN")
  BETWEEN       @map("BETWEEN")
}

enum FlowNodeScheduledActionStatus {
  scheduled
  queuing
  enqueued
  completed
  failed
}

enum InitialPromptType {
  generate_whatsapp_message_template
}

enum AudienceRecommendationClassSlug {
  champion
  new
  inactive
}

enum SourceIntegration {
  hubspot_crm
  magento2_ecommerce
  nuvem_shop
  vtex_ecommerce
  file_import
  direct_message
  unknown
  shopify_ecommerce
  visual_ecommerce
  loja_integrada
  woo_commerce
  bling
  magazord
  unbox
  magento1_ecommerce
  ingresse
  yampi
  google_tag_manager
  omny
  tray
  omie
  cartPanda
  linx_commerce
  shoppub
  custom
  tiny
  vnda
  millennium
  varejo_online
  venda_ai
  website
  irroba
  wake_commerce
}

enum AutomationTypeSlug {
  tracking_code
  abandoned_cart
  new_order
  welcome_registration
  order_confirmation
  order_payment_confirmation
  custom
  order_status_update
}

enum CompanyDefinedFieldDataType {
  string
  number
  boolean
  date
}

enum CompanyDefinedFieldTable {
  customers
}

enum FilterType {
  customer
}

enum ColumnMappingConfigTable {
  orders
}

enum AutomaticReplyCondition {
  contains
  equals
}

enum BusinessSector {
  supplements_and_other_foods
  clothing_and_accessories
  wellness_and_cosmetics
  events_and_entertainment
}

enum ConversationTicketStatus {
  open
  closed
}

enum CouponType {
  cashback
}

enum DiscountType {
  percentage
  fixed_in_cents
}

enum CouponStep {
  pending
  creation_whatsapp_sent
  reminder_whatsapp_sent
  last_day_whatsapp_sent
}

enum MessageStatus {
  read
  failed
  delivered
  sent
  enqueued
  mismatch
}

enum MediaType {
  audio
  file
  image
  video
  sticker
}

enum CardHeaderType {
  IMAGE
  VIDEO
}

enum MessageTemplateStatus {
  pending
  rejected
  approved
  deleted
  disabled
  paused
}

enum WhatsappTemplateCategory {
  MARKETING
  AUTHENTICATION
  UTILITY
}

enum MessageTemplateType {
  MARKETING
  REVIEW_REQUEST
  INITIAL_MESSAGE
  ABANDONED_CART
  TRACKING_CODE
  NEW_ORDER
  WELCOME_REGISTRATION
  ORDER_CONFIRMATION
  ORDER_PAYMENT_CONFIRMATION
  ORDER_STATUS_UPDATE
  CASHBACK
}

enum CustomerEmailState {
  pending
  verified
  opted_out
}

enum EmailTemplateType {
  MARKETING
  TRANSACTIONAL
  ABANDONED_CART
  TRACKING_CODE
  ORDER_STATUS_UPDATE
}

enum EmailCategory {
  MARKETING
  TRANSACTIONAL
  UTILITY
}

enum EmailStatus {
  sent
  failed
  delivered
  opened
  engaged
  tagged_as_spam
}

enum EmailCampaignStatus {
  in_progress
  completed
  interrupted
  scheduled
  canceled
  failed
}

enum EmailProvider {
  ses
  sendgrid
}

enum GupshupTemplateType {
  TEXT
  IMAGE
  LOCATION
  PRODUCT
  CATALOG
  LTO
  CAROUSEL
  VIDEO
  DOCUMENT
}

enum WhatsappCampaignStatus {
  in_progress
  completed
  interrupted
  scheduled
  canceled
  failed
}

enum ExperimentStatus {
  scheduled
  in_progress
  completed
  canceled
}

enum SmsCampaignStatus {
  in_progress
  completed
  scheduled
  canceled
  failed
}

enum LogSeverity {
  error
  info
  warning
}

enum LogSource {
  whatsapp_cloud_api
  gupshup
  internal
  vtex
  shopify
  sms_legal
  visual_ecommerce
  eyou
  loja_integrada
  woo_commerce
  bling
  magazord
  unbox
  magento2_ecommerce
  nuvem_shop
  magento1_ecommerce
  yampi
  revi_public_api
  omny
  tray
  omie
  cartPanda
  linx_commerce
  shoppub
  ingresse
  revi_plataform
  custom_integration
  tiny
  vnda
  millennium
  varejo_online
  venda_ai
  sendgrid
  irroba
  wake_commerce
}

enum LogType {
  syncOrders
  syncAbandonedCarts
  sendAbandonedCartMessages
  executeCompanyCustomPeriodicTask
  connectingDatabase
  sendTrackingCodeMessages
  sendTrackingCodeEmails
  sendOrderStatusUpdateMessages
  sendOrderStatusUpdateEmails
  reviPublicApiCall
  orderEvents
  syncCustomers
  scheduleAbandonedCarts
  userActivity
  handleEmailNotifications
  sendEmail
  couponEvents
  sendCouponMessage
  generateAiAgentSuggestion
  selectAiAgentSuggestion
  sendAiAgentSuggestion
  syncProducts
  emailEvents
  emailConfiguration
  updateCustomers
  cacheManagement
  apiTracking
  whatsappCloudApiCall
  receiveWhatsappCartOrders
  productEvents
}

enum CommunicationChannel {
  whatsapp
  sms
}

enum OrderSourceType {
  file_import
  integration
}

enum AbandonedCartStatus {
  abandoned
  whatsapp_sent
  email_sent
  whatsapp_and_email_sent
  flow_triggered
  failed
}

enum CampaignType {
  standard
  experiment_test_group
  experiment_winning_group
}

enum InvoiceStatus {
  pending
  paid
  canceled
}

enum PaymentMethod {
  boleto
  credit_card
  pix
}

enum AutomationAction {
  send_message_template
  trigger_flow
}

enum AppModule {
  HOME
  CHAT
  CUSTOMERS
  CAMPAIGNS
  TEMPLATES
  AUTOMATIONS
  SETTINGS
  REPORTS
  DEBUG_TOOLS
  PRODUCTS
}

enum CustomersTableHeader {
  name
  phone_number_id
  email
  customer_tags
  default_agent_name
  state
  city
  total_orders
  total_purchases
  average_order_value
  last_purchase_at
  birth_date
  created_at
  is_opted_out
}

model Role {
  id                   String      @id @default(uuid())
  name                 String
  appModulePermissions AppModule[] @default([]) @map("app_module_permissions")
  companyId            String      @map("company_id")
  company              Company     @relation(fields: [companyId], references: [id])
  createdAt            DateTime    @default(now()) @map("created_at")
  updatedAt            DateTime    @default(now()) @updatedAt @map("updated_at")
  users                User[]

  @@map("roles")
}

model WebPushSubscription {
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")
  id        String   @id @default(uuid())
  userId    String   @map("user_id")
  companyId String   @map("company_id")
  userAgent String   @map("user_agent")
  endpoint  String   @map("endpoint")
  p256dh    String   @map("p256dh")
  auth      String   @map("auth")
  isActive  Boolean? @default(true) @map("is_active")

  user      User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  company   Company          @relation(fields: [companyId], references: [id], onDelete: Cascade)
  histories WebPushHistory[]

  @@unique([userId, companyId, endpoint, p256dh, auth])
  @@map("web_push_subscriptions")
}

enum WebPushType {
  notification
}

model WebPushHistory {
  createdAt      DateTime    @default(now()) @map("created_at")
  updatedAt      DateTime    @default(now()) @updatedAt @map("updated_at")
  id             String      @id @default(uuid())
  subscriptionId String      @map("subscription_id")
  payload        Json        @map("payload")
  type           WebPushType @map("type")

  subscription WebPushSubscription @relation(fields: [subscriptionId], references: [id])

  @@map("web_push_histories")
}

model ApiRequestsLogs {
  createdAt          DateTime @default(now()) @map("created_at")
  updatedAt          DateTime @default(now()) @updatedAt @map("updated_at")
  id                 String   @id @default(uuid())
  controller         String   @map("controller")
  handler            String   @map("handler")
  url                String?  @map("url")
  method             String?  @map("method")
  companyId          String?  @map("company_id")
  userId             String?  @map("user_id")
  userIp             String?  @map("user_ip")
  userAgent          String?  @map("user_agent")
  requestHeaders     Json?    @map("request_headers")
  requestBody        Json?    @map("request_body")
  requestQueryParams Json?    @map("request_query_params")
  responseBody       Json?    @map("response_body")
  error              Json?    @map("error")
  user               User?    @relation(fields: [userId], references: [id])
  company            Company? @relation(fields: [companyId], references: [id])

  @@map("api_requests_logs")
}

model Cart {
  createdAt  DateTime           @default(now()) @map("created")
  updatedAt  DateTime           @default(now()) @updatedAt @map("updated")
  id         String             @id @default(dbgenerated("gen_random_uuid()"))
  companyId  String             @map("company_id")
  customerId String             @map("customer_id")
  status     String
  source     SourceIntegration?
  company    Company            @relation(fields: [companyId], references: [id])
  customer   Customer           @relation(fields: [customerId], references: [id])
  cartItem   CartItem[]

  @@map("carts")
}

model CartItem {
  createdAt        DateTime @default(now()) @map("created")
  updatedAt        DateTime @default(now()) @updatedAt @map("updated")
  id               String   @id @default(dbgenerated("gen_random_uuid()"))
  cartId           String   @map("cart_id")
  productVariantId String   @map("product_variant_id")
  quantity         Int      @map("quantity")
  price            Int

  cart           Cart           @relation(fields: [cartId], references: [id])
  productVariant ProductVariant @relation(fields: [productVariantId], references: [id])

  @@unique([cartId, productVariantId])
  @@index([cartId])
  @@index([cartId, productVariantId])
  @@map("cart_items")
}

model AIAgentChatInfo {
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @default(now()) @updatedAt @map("updated_at")
  id         String   @id @default(dbgenerated("gen_random_uuid()"))
  summary    String   @map("summary")
  agentName  String   @map("agent_name")
  customerId String   @map("customer_id")
  metadata   Json?
  customer   Customer @relation(fields: [customerId], references: [id])

  @@map("ai_agent_chat_infos")
}
