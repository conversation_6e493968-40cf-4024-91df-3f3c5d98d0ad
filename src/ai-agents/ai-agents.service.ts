import { Injectable } from '@nestjs/common';
import { MediaType } from '@prisma/client';
import axios from 'axios';
import { CompanyWithIncludes } from 'src/companies/types/CompanyWithIncludes';
import { ConversationWithIncludes } from 'src/conversations/types/ConversationWithIncludes';
import { FilesService } from 'src/files/files.service';
import { SendProductCatalogDto } from 'src/messages/dto/send-product-catalog.dto';
import { MessagesService } from 'src/messages/messages.service';

@Injectable()
export class AiAgentsService {
  constructor(
    private readonly filesService: FilesService,
    private readonly messagesService: MessagesService,
  ) {}

  async processCustomerMessage({
    company,
    message,
    conversation,
  }: {
    company: CompanyWithIncludes;
    message: string;
    conversation: ConversationWithIncludes;
  }) {
    try {
      const requisition = await axios.post(
        'http://127.0.0.1:5678/webhook/agent-message',
        {
          message: message,
          customerId: conversation.customerId,
          companyId: conversation.companyId,
          conversationId: conversation.id,
        },
        {
          headers: {
            accept: 'application/json',
            'Content-Type': 'application/json',
          },
        },
      );
      const response = (
        typeof requisition.data !== 'string'
          ? requisition.data
          : JSON.parse(requisition.data)
      ) as {
        image_url: string[];
        messages: string[];
        catalog?: SendProductCatalogDto;
      };
      if (response.image_url.length === 1) {
        const file = await this.filesService.downloadFileFromUrl(
          response.image_url[0],
        );
        const uploadedFile = await this.filesService.uploadFile({
          companyId: conversation.companyId,
          file,
          keyPrefix: 'ai-agent-replies/media',
        });
        if (uploadedFile) {
          await this.messagesService.sendMessage({
            isAutomaticResponse: true,
            companyId: conversation.companyId,
            senderPhoneNumberId: company.phoneNumberId,
            conversationId: conversation.id,
            mediaType: uploadedFile.mediaType || MediaType.image,
            fileKey: uploadedFile.key,
            text: response.messages[0],
          });
        }
      }

      for (const message of response.messages) {
        await this.messagesService.sendMessage({
          isAutomaticResponse: true,
          companyId: conversation.companyId,
          senderPhoneNumberId: company.phoneNumberId,
          conversationId: conversation.id,
          text: message,
        });
      }
      if (response.catalog) {
        response.catalog.sections = response.catalog.sections?.map(
          (section) => ({
            ...section,
            title: 'Destaques',
          }),
        );
        await this.messagesService.sendProductCatalog(response.catalog);
      }
      return true;
    } catch (error) {
      await this.messagesService.sendMessage({
        isAutomaticResponse: true,
        companyId: conversation.companyId,
        senderPhoneNumberId: company.phoneNumberId,
        conversationId: conversation.id,
        text: 'Ocorreu um erro, poderia tentar novamente?',
      });
      return true;
    }
  }
}
